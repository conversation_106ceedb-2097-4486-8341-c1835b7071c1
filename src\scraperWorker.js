const cron = require("node-cron");
const scrapeAmazonReviews = require("./utils/reviewScraper");
const prisma = require("../prisma/prismaClient");

const runScraperWorker = async () => {
  try {
    // Check if any ASIN is already in progress
    const inProgress = await prisma.aSIN.findFirst({
      where: { status: "IN_PROGRESS" },
    });

    if (inProgress) {
      console.log("⏳ Another process is running. Waiting for next cycle...");
      return; // Skip this cron run
    }

    // Fetch PENDING ASINs
    const asins = await prisma.aSIN.findMany({
      where: { status: "PENDING" },
    });

    if (asins.length === 0) {
      console.log("📭 No ASINs to process.");
      return;
    }

    for (const asinEntry of asins) {
      await prisma.aSIN.update({
        where: { id: asinEntry.id },
        data: { status: "IN_PROGRESS" },
      });

      const asin = asinEntry.asin;
      const cookieKey = JSON.parse(asinEntry.cookieKey);
      console.log(`🔧 Processing ASIN: ${asin}`);

      const result = await scrapeAmazonReviews(asin, cookieKey, asinEntry.id);

      await prisma.aSIN.update({
        where: { id: asinEntry.id },
        data: { status: result.success ? "SUCCESS" : "FAILED" },
      });

      console.log(
        result.success
          ? `✅ Scraping and saving successful for ASIN: ${asin}`
          : `⚠️ Scraping failed for ASIN: ${asin}`
      );
    }

    console.log("🎉 All ASINs processed.");
  } catch (error) {
    console.error("❌ Worker error:", error.message);
  }
};


runScraperWorker();
// Cron job runs every minute
cron.schedule("* * * * *", runScraperWorker);

module.exports = { runScraperWorker };
