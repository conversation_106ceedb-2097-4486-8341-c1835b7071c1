const express = require("express");
const runScraperWorker = require("./src/scraperWorker"); 

const app = express();
const PORT = 3000;

const scraperRoutes = require("./src/routes/scraperRoutes");
const clearDB = require("./src/utils/cleanDB");

app.use(express.json());
app.use("/api", scraperRoutes);


app.listen(PORT, () => {
  console.log(`Scraper server running at http://localhost:${PORT}`);
});
