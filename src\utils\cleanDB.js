const prisma = require("../../prisma/prismaClient");

const clearDB = async () => {
  try {
    await prisma.review.deleteMany();
    await prisma.aSIN.deleteMany();

    // Reset the auto-increment counters
    await prisma.$executeRawUnsafe(
      `DELETE FROM sqlite_sequence WHERE name='Review';`
    );
    await prisma.$executeRawUnsafe(
      `DELETE FROM sqlite_sequence WHERE name='ASIN';`
    );

    console.log("✅ Database cleared and auto-increment counters reset.");
  } catch (err) {
    console.error("❌ Error clearing database:", err.message);
  }
};

module.exports = clearDB;