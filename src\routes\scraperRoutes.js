const express = require("express");
const router = express.Router();
const { PrismaClient } = require("@prisma/client");
const clearDB = require("../utils/cleanDB");
const prisma = new PrismaClient();

// POST /create_job - Accepts up to 5 ASINs and adds them
router.post("/create_job", async (req, res) => {
  const { asins, cookieKey } = req.body; // Expecting: { asins: ['ASIN1', 'ASIN2', ...] }

  if (!asins || !Array.isArray(asins) || asins.length === 0) {
    return res.status(400).json({ error: "ASIN list is required" });
  }

  if (asins.length > 5) {
    return res.status(400).json({ error: "Maximum 5 ASINs allowed per batch" });
  }

  const cookieKeyString = JSON.stringify(cookieKey);

  try {
    const asinRecords = await Promise.all(
      asins.map((asin) =>
        prisma.aSIN.upsert({
          where: { asin },
          update: { cookieKey:cookieKeyString },
          create: { asin, cookieKey:cookieKeyString },
        })
      )
    );
    console.log("Asin added:", asinRecords.length);
    res.json({ message: "ASINs added", data: asinRecords });
  } catch (err) {
    console.log("ERROR while creating job:", err);
    res.status(500).json({ error: "Error adding ASINs", details: err.message });
  }
});

// GET /get_reviews - Returns all reviews
router.get("/get_reviews", async (req, res) => {
  try {
    const asins = await prisma.aSIN.findMany({
      select: {
        asin: true,
        status: true,
        reviews: true,
      }
    });
    res.json(asins);
  } catch (err) {
    res
      .status(500)
      .json({ error: "Error fetching reviews", details: err.message });
  }
});


router.post("/clear_db", async (req, res) => {
  try {
    await clearDB();
    res.json({ message: "Database cleared and counters reset." });
  } catch (err) {
    console.error("❌ Error clearing DB via route:", err.message);
    res
      .status(500)
      .json({ error: "Error clearing database", details: err.message });
  }
});

module.exports = router;
