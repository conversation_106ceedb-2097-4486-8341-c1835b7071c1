-- CreateTable
CREATE TABLE "ASIN" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "asin" TEXT NOT NULL,
    "cookieKey" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'PENDING'
);

-- CreateTable
CREATE TABLE "Review" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "reviewId" TEXT,
    "reviewURL" TEXT,
    "data" TEXT,
    "asinId" INTEGER NOT NULL,
    CONSTRAINT "Review_asinId_fkey" FOREIGN KEY ("asinId") REFERENCES "ASIN" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "ASIN_asin_key" ON "ASIN"("asin");
