{"name": "lex-review-scraper", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.9.0", "eslint": "^9.29.0", "express": "^5.1.0", "node-cron": "^4.1.0", "prisma": "^6.9.0", "puppeteer": "^24.10.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "sqlite3": "^5.1.7"}}