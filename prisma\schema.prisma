// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
 url      = "file:./test.db"
}

model ASIN {
  id       Int      @id @default(autoincrement())
  asin     String   @unique
  cookieKey String
  status   Status   @default(PENDING)
  reviews  Review[]
}

model Review {
  id        Int     @id @default(autoincrement())
  reviewId  String?
  reviewURL String?
  data      String?
  asinId    Int
  asin      ASIN    @relation(fields: [asinId], references: [id])
}

enum Status {
  PENDING
  IN_PROGRESS
  SUCCESS
  FAILED
}
