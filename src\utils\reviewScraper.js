const puppeteer = require("puppeteer-extra");
const StealthPlugin = require("puppeteer-extra-plugin-stealth");
const fs = require("fs").promises;
const prisma = require("../../prisma/prismaClient");

// Add stealth plugin
puppeteer.use(StealthPlugin());

// Selectors from your content script
const selectors = {
  allReviews: "#cm_cr-review_list",
  reviewItems: ".review.aok-relative",
  authorName: ".a-profile-name",
  authorNameAlt:
    ".customerReviewsMobileFeature .a-profile-content span.a-profile-name",
  authorLink: "div[data-hook='genome-widget'] a, .a-profile",
  reviewDate: "span[data-hook='review-date']",
  reviewTitle: ".review-title-content",
  reviewBody: ".review-text-content>span",
  reviewRating:
    "i[data-hook='review-star-rating']>span, i[data-hook='cmps-review-star-rating']>span, .a-icon-alt",
  verifiedPurchase: "span[data-hook='avp-badge']",
  reviewImg: "img.review-image-tile",
  helpfulCounts: ".cr-vote-text",
  productTitle: ".product-title, #productTitle",
  variantsElement: "a[data-hook='format-strip']",
  nextButton: "li.a-last a",
  disabledNextButton: "li.a-disabled.a-last",
};

function parseCookiesToString(cookieArray) {
  if (!Array.isArray(cookieArray) || cookieArray.length === 0) {
    return "";
  }

  const cookieStrings = cookieArray
    .filter((cookie) => cookie && cookie.name && cookie.value) // Filter out invalid cookies
    .map((cookie) => `${cookie.name}=${cookie.value}`);

  return cookieStrings.join("; ");
}

async function saveReviewsToDB(reviews, asinId) {
  const reviewsToCreate = reviews.map((review) => ({
    reviewId: review["Review ID"] || "",
    reviewURL: review["Review URL"] || "",
    data: JSON.stringify(review),
    asinId: asinId,
  }));

  // Save in batches of 10
  for (let i = 0; i < reviewsToCreate.length; i += 10) {
    const batch = reviewsToCreate.slice(i, i + 10);
    await prisma.review.createMany({ data: batch });
    console.log(
      `💾 Saved batch ${Math.floor(i / 10) + 1} of ${Math.ceil(
        reviewsToCreate.length / 10
      )}`
    );
  }
}


async function scrapeAmazonReviews(asin, cookies, asinId, brand = "") {
  let browser;
  const allReviews = [];

  try {
    console.log(`Starting scraper for ASIN: ${asin}`);

    // Launch browser with stealth mode
    browser = await puppeteer.launch({
      headless: true, // Set to true for production
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--no-first-run",
        "--no-zygote",
        "--disable-gpu",
      ],
    });

    const page = await browser.newPage();

    // Set latest user agent
    await page.setUserAgent(
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36"
    );
    const parsedCookies = parseCookiesToString(cookies);
    // Set cookies if provided
    if (cookies && cookies.length > 0) {
      await page.setCookie(...cookies);
    }

    // Set viewport
    await page.setViewport({ width: 1366, height: 768 });

    // Loop through 1, 2, 3 star ratings
    for (const starRating of [1, 2, 3]) {
      console.log(`\n=== Scraping ${starRating}-star reviews ===`);

      const starReviews = await scrapeStarRating(page, asin, starRating, brand);
      allReviews.push(...starReviews);
      if (starReviews.length > 0) {
        await saveReviewsToDB(starReviews, asinId);
        console.log(
          `✅ Saved ${starReviews.length} reviews for ${starRating}-star`
        );
      }
      
      // Wait between star ratings to avoid being blocked
      await new Promise((resolve) => setTimeout(resolve, 2000));
    }

    console.log(`\nTotal reviews collected: ${allReviews.length}`);

    // Save to CSV
    // await saveToCSV(allReviews, asin);

    return {
      success: true,
      totalReviews: allReviews.length,
      reviews: allReviews,
    };
  } catch (error) {
    console.error("Error in scrapeAmazonReviews:", error);
    return {
      success: false,
      error: error.message,
      reviews: allReviews,
    };
  } finally {
    if (browser) {
      //   await browser.close(); // Make sure to close the browser
    }
  }
}

async function scrapeStarRating(page, asin, starRating, brand) {
  const reviews = [];
  let hasNextPage = true;
  let pageCount = 0;

  try {
    // Navigate to reviews page with star filter
    const filterParam = getStarFilterParam(starRating);
    const reviewsUrl = `https://www.amazon.com/product-reviews/${asin}?filterByStar=${filterParam}&pageNumber=1`;

    console.log(`Navigating to: ${reviewsUrl}`);
    await page.goto(reviewsUrl, { waitUntil: "networkidle2", timeout: 30000 });

    while (hasNextPage && pageCount < 50) {
      // Limit to 50 pages per star rating
      pageCount++;
      console.log(`Scraping page ${pageCount} for ${starRating}-star reviews`);

      // Wait for review list to load
      try {
        await page.waitForSelector(selectors.allReviews, { timeout: 10000 });
      } catch (error) {
        console.log(
          "No reviews found on this page, moving to next star rating"
        );
        break;
      }

      // Extract reviews from current page
      const pageReviews = await page.evaluate(
        (selectors, asin, brand) => {
          // Helper functions in page context
          const extractText = (element, selector) => {
            const el = element.querySelector(selector);
            return el ? el.textContent.trim() : "";
          };

          const extractTitleText = (parentElement, targetSelector) => {
            const targetElement = parentElement.querySelector(targetSelector);
            if (!targetElement) return "";

            const directChildSpans =
              targetElement.querySelectorAll(":scope > span");
            return Array.from(directChildSpans)
              .map((span) => (span.textContent || "").trim())
              .filter((text) => text.length > 0)
              .join(" ");
          };

          const elementExists = (element, selector) => {
            return element.querySelector(selector) !== null;
          };

          const extractHelpfulCounts = (element) => {
            try {
              const helpfulText = extractText(element, selectors.helpfulCounts);
              if (!helpfulText || helpfulText.trim() === "") return 0;

              if (helpfulText.startsWith("One person") || helpfulText === "One")
                return 1;

              const match = helpfulText.match(/(\d+)/);
              return match ? parseInt(match[1]) : 0;
            } catch (error) {
              return 0;
            }
          };

          const getImgArray = (element) => {
            try {
              const images = [];
              const imgElements = element.querySelectorAll(selectors.reviewImg);

              for (let i = 0; i < imgElements.length && i < 4; i++) {
                const imageUrl = imgElements[i].getAttribute("src") || "";
                if (imageUrl) images.push(imageUrl);
              }

              while (images.length < 4) {
                images.push("");
              }

              return images;
            } catch (error) {
              return ["", "", "", ""];
            }
          };

          const extractCountryAndDate = (dateString) => {
            try {
              const regex = /Reviewed in (?:the )?(.*?) on (.*)/i;
              const match = dateString.match(regex);

              if (match && match.length === 3) {
                return {
                  reviewerCountry: match[1].trim(),
                  reviewDate: match[2].trim(),
                };
              }

              const altRegex = /Reviewed on (.*)/i;
              const altMatch = dateString.match(altRegex);

              if (altMatch && altMatch.length === 2) {
                return {
                  reviewerCountry: "",
                  reviewDate: altMatch[1].trim(),
                };
              }

              return {
                reviewerCountry: "",
                reviewDate: dateString,
              };
            } catch (error) {
              return {
                reviewerCountry: "",
                reviewDate: dateString,
              };
            }
          };

          const segregateVariantText = (parentElement, selector) => {
            if (
              !parentElement ||
              typeof parentElement.querySelector !== "function"
            ) {
              return {};
            }

            const anchorTag = parentElement.querySelector(selector);
            if (!anchorTag || anchorTag.tagName.toLowerCase() !== "a") {
              return {};
            }

            const variants = {};
            let currentIndex = 0;
            let currentTextAccumulator = "";

            for (const node of anchorTag.childNodes) {
              if (node.nodeType === Node.TEXT_NODE) {
                currentTextAccumulator += node.textContent.trim();
              } else if (
                node.nodeType === Node.ELEMENT_NODE &&
                node.tagName === "I" &&
                node.classList.contains("a-icon-text-separator")
              ) {
                if (currentTextAccumulator) {
                  variants[`variant${currentIndex}`] = currentTextAccumulator;
                  currentIndex++;
                  currentTextAccumulator = "";
                }
              }
            }

            if (currentTextAccumulator) {
              variants[`variant${currentIndex}`] = currentTextAccumulator;
            }

            return variants;
          };

          // Main extraction logic
          const reviewItems = document.querySelectorAll(selectors.reviewItems);
          const reviews = [];

          // Get product title
          const productTitleElement = document.querySelector(
            selectors.productTitle
          );
          const productTitle = productTitleElement
            ? productTitleElement.textContent.trim()
            : "";

          reviewItems.forEach((review) => {
            try {
              const reviewerId = review.id || "";

              // Extract rating
              const ratingText = extractText(review, selectors.reviewRating);
              let rating = 0;
              if (ratingText) {
                const ratingMatch = ratingText.match(/(\d+(\.\d+)?)/);
                rating = ratingMatch ? parseFloat(ratingMatch[1]) : 0;
              }

              const description = extractText(review, selectors.reviewBody);
              const title = extractTitleText(review, selectors.reviewTitle);
              const reviewLink = `https://www.amazon.com/gp/customer-reviews/${reviewerId}`;
              const dateText = extractText(review, selectors.reviewDate);
              const { reviewerCountry, reviewDate } =
                extractCountryAndDate(dateText);

              const authorElem = review.querySelector(selectors.authorName);
              const authorAltElem = review.querySelector(
                selectors.authorNameAlt
              );
              const username = authorElem
                ? authorElem.textContent.trim()
                : authorAltElem
                ? authorAltElem.textContent.trim()
                : "";

              const authorLinkElem = review.querySelector(selectors.authorLink);
              const userLinkHref = authorLinkElem
                ? authorLinkElem.getAttribute("href")
                : "";
              const userLink = userLinkHref
                ? userLinkHref.startsWith("http")
                  ? userLinkHref
                  : `https://www.amazon.com${userLinkHref}`
                : "";

              const isVerified = elementExists(
                review,
                selectors.verifiedPurchase
              )
                ? "True"
                : "False";
              const HelpfulCounts = extractHelpfulCounts(review);
              const images = getImgArray(review);
              const productLink = `https://www.amazon.com/dp/${asin}`;
              const pageUrl = window.location.href;

              const variants = segregateVariantText(
                review,
                selectors.variantsElement
              );

              const reviewData = {
                ASIN: asin,
                "Seller Name": brand,
                HelpfulCounts: HelpfulCounts,
                image1: images[0],
                image2: images[1],
                image3: images[2],
                image4: images[3],
                pageUrl,
                "Review ID": reviewerId,
                productLink,
                productTitle,
                reviewContent: description,
                reviewerCountry,
                reviewDate: reviewDate,
                reviewScore: rating,
                "Review Title": title,
                reviewer: username,
                "Review URL": reviewLink,
                reviewerLink: userLink,
                variant_0: variants.variant0 || "",
                variant_1: variants.variant1 || "",
                isVerified,
              };

              reviews.push(reviewData);
            } catch (error) {
              console.error("Error extracting review:", error);
            }
          });

          return reviews;
        },
        selectors,
        asin,
        brand
      );

      reviews.push(...pageReviews);
      console.log(
        `Extracted ${pageReviews.length} reviews from page ${pageCount}`
      );

      // Check for next page and click it
      hasNextPage = await page.evaluate((selectors) => {
        const nextButton = document.querySelector(selectors.nextButton);
        const disabledNextButton = document.querySelector(
          selectors.disabledNextButton
        );

        if (nextButton && !disabledNextButton) {
          nextButton.click();
          return true;
        }
        return false;
      }, selectors);

      if (hasNextPage) {
        // Wait for next page to load - FIXED: Use setTimeout instead of page.waitForTimeout
        await new Promise((resolve) => setTimeout(resolve, 3000));
        try {
          await page.waitForSelector(selectors.allReviews, { timeout: 10000 });
        } catch (error) {
          console.log("Next page did not load properly, stopping pagination");
          hasNextPage = false;
        }
      }
    }
  } catch (error) {
    console.error(`Error scraping ${starRating}-star reviews:`, error);
  }

  return reviews;
}

function getStarFilterParam(starRating) {
  switch (starRating) {
    case 1:
      return "one_star";
    case 2:
      return "two_star";
    case 3:
      return "three_star";
    case 4:
      return "four_star";
    case 5:
      return "five_star";
    default:
      return "one_star";
  }
}

module.exports = scrapeAmazonReviews;
